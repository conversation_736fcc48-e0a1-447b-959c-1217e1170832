<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'member_id',
        'branch_id',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the branch that the user belongs to.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the member profile if the user is a member.
     */
    public function memberProfile(): BelongsTo
    {
        return $this->belongsTo(Member::class, 'member_id');
    }

    /**
     * Get the branches managed by this user.
     */
    public function managedBranches(): HasMany
    {
        return $this->hasMany(Branch::class, 'manager_id');
    }

    /**
     * Get the loan applications reviewed by this user.
     */
    public function reviewedLoanApplications(): HasMany
    {
        return $this->hasMany(LoanApplication::class, 'reviewed_by');
    }

    /**
     * Get the installments collected by this user.
     */
    public function collectedInstallments(): HasMany
    {
        return $this->hasMany(Installment::class, 'collected_by');
    }

    /**
     * Get the branch transactions entered by this user.
     */
    public function branchTransactions(): HasMany
    {
        return $this->hasMany(BranchTransaction::class, 'entered_by');
    }

    /**
     * Get the saving accounts created by this user.
     */
    public function createdSavingAccounts(): HasMany
    {
        return $this->hasMany(SavingAccount::class, 'created_by');
    }

    /**
     * Get the members created by this user.
     */
    public function createdMembers(): HasMany
    {
        return $this->hasMany(Member::class, 'created_by');
    }

    /**
     * Check if user is admin.
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if user is manager.
     */
    public function isManager(): bool
    {
        return $this->role === 'manager';
    }

    /**
     * Check if user is field officer.
     */
    public function isFieldOfficer(): bool
    {
        return $this->role === 'field_officer';
    }

    /**
     * Check if user is member.
     */
    public function isMember(): bool
    {
        return $this->role === 'member';
    }

    /**
     * Scope for active users.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for users by role.
     */
    public function scopeByRole($query, string $role)
    {
        return $query->where('role', $role);
    }
}
