<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Member extends Model
{
    protected $fillable = [
        'member_id',
        'name',
        'father_or_husband_name',
        'mother_name',
        'present_address',
        'permanent_address',
        'nid_number',
        'date_of_birth',
        'religion',
        'phone_number',
        'blood_group',
        'photo',
        'occupation',
        'reference_id',
        'branch_id',
        'created_by',
        'is_active',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * Get the branch this member belongs to
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the user who created this member
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the reference member
     */
    public function reference(): BelongsTo
    {
        return $this->belongsTo(Member::class, 'reference_id');
    }

    /**
     * Get members who reference this member
     */
    public function referencedMembers(): HasMany
    {
        return $this->hasMany(Member::class, 'reference_id');
    }

    /**
     * Get all loan applications for this member
     */
    public function loanApplications(): HasMany
    {
        return $this->hasMany(LoanApplication::class);
    }

    /**
     * Get all saving accounts for this member
     */
    public function savingAccounts(): HasMany
    {
        return $this->hasMany(SavingAccount::class);
    }

    /**
     * Get the user account associated with this member
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'id', 'member_id');
    }
}
