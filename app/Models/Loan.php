<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Loan extends Model
{
    use HasFactory;

    protected $fillable = [
        'loan_application_id',
        'loan_date',
        'loan_amount',
        'total_repayment_amount',
        'repayment_duration',
        'repayment_method',
        'installment_count',
        'installment_amount',
        'advance_payment',
        'first_installment_date',
        'last_installment_date',
    ];

    protected $casts = [
        'loan_date' => 'date',
        'first_installment_date' => 'date',
        'last_installment_date' => 'date',
        'loan_amount' => 'decimal:2',
        'total_repayment_amount' => 'decimal:2',
        'installment_amount' => 'decimal:2',
        'advance_payment' => 'decimal:2',
    ];

    /**
     * Get the loan application that owns the loan.
     */
    public function loanApplication(): BelongsTo
    {
        return $this->belongsTo(LoanApplication::class);
    }

    /**
     * Get the member through the loan application.
     */
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class, 'member_id', 'id')
            ->through('loanApplication');
    }

    /**
     * Get the installments for the loan.
     */
    public function installments(): HasMany
    {
        return $this->hasMany(Installment::class);
    }

    /**
     * Get the pending installments.
     */
    public function pendingInstallments(): HasMany
    {
        return $this->installments()->where('status', 'pending');
    }

    /**
     * Get the paid installments.
     */
    public function paidInstallments(): HasMany
    {
        return $this->installments()->where('status', 'paid');
    }

    /**
     * Get the overdue installments.
     */
    public function overdueInstallments(): HasMany
    {
        return $this->installments()->where('status', 'overdue');
    }

    /**
     * Calculate total paid amount.
     */
    public function getTotalPaidAttribute(): float
    {
        return $this->paidInstallments()->sum('installment_amount');
    }

    /**
     * Calculate remaining balance.
     */
    public function getRemainingBalanceAttribute(): float
    {
        return $this->total_repayment_amount - $this->total_paid;
    }

    /**
     * Check if loan is fully paid.
     */
    public function getIsFullyPaidAttribute(): bool
    {
        return $this->remaining_balance <= 0;
    }
}
